package com.yyigou.ddc.dmp.model.req.dataset;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DatasetSaveReq implements Serializable {
    private String enterpriseNo;
    private String datasetNo;
    private String datasetCode;
    private String datasetName;
    private String drivingSchemaName;
    private String drivingTableName;
    private String description;
    private Integer status;
    private Integer deleted;
    private String createNo;
    private String createName;
    private String createTime;
    private String modifyNo;
    private String modifyName;
    private String modifyTime;
    private LocalDateTime opTimestamp;

    private List<DatasetJoinRelSaveReq> datasetJoinRels;
    private List<DatasetFieldsSaveReq> datasetFields;
}
