package com.yyigou.ddc.dmp.service.dataset.impl;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yyigou.ddc.dmp.common.enums.DeletedEnum;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.dao.SqlExtractor;
import com.yyigou.ddc.dmp.dao.dataset.entity.Dataset;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetField;
import com.yyigou.ddc.dmp.dao.dataset.entity.DatasetJoinRel;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetFieldMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetJoinRelMapper;
import com.yyigou.ddc.dmp.dao.dataset.mapper.DatasetMapper;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetJoinRelSaveReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetFieldsDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetJoinRelDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.service.meta.MetaService;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class DatasetServiceImpl extends ServiceImpl<DatasetMapper, Dataset> implements DatasetService {
    @Resource
    private DatasetJoinRelMapper datasetJoinRelMapper;

    @Resource
    private DatasetFieldMapper datasetFieldMapper;

    @Resource
    private MetaService metaService;

    @Resource
    private SqlExtractor sqlExtractor;

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    @Override
    @Transactional
    public String saveDataset(DatasetSaveReq datasetSaveReq) {
        Dataset dataset = BeanCopyUtil.copyFields(datasetSaveReq, Dataset.class);
        if (StringUtils.isEmpty(dataset.getDatasetNo())) {
            //新增
            dataset.setDatasetNo(UUID.fastUUID().toString());
        } else {
            boolean exists = exists(Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );

            if (!exists) {
                throw new BusinessException("数据集不存在");
            }

            Dataset toDeleteDataset = new Dataset();
            toDeleteDataset.setDeleted(DeletedEnum.DELETED.getValue());

            update(toDeleteDataset, Wrappers.<Dataset>lambdaQuery()
                    .eq(Dataset::getEnterpriseNo, dataset.getEnterpriseNo())
                    .eq(Dataset::getDatasetNo, dataset.getDatasetNo())
                    .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
            );
        }
        save(dataset);

        List<DatasetJoinRel> datasetJoinRels = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetJoinRels(), DatasetJoinRel.class);
        replaceDatasetJoinRels(dataset, datasetJoinRels);

        List<DatasetField> datasetFields = BeanCopyUtil.copyFieldsList(datasetSaveReq.getDatasetFields(), DatasetField.class);
        replaceDatesetFields(dataset, datasetFields);

        return dataset.getDatasetNo();
    }

    private void replaceDatasetJoinRels(Dataset dataset, List<DatasetJoinRel> datasetJoinRels) {
        datasetJoinRelMapper.delete(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        datasetJoinRelMapper.insert(datasetJoinRels);
    }

    private void replaceDatesetFields(Dataset dataset, List<DatasetField> datasetFields) {
        datasetFieldMapper.delete(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, dataset.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, dataset.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        datasetFieldMapper.insert(datasetFields);
    }

    @Override
    public DatasetDetailRes getDataset(DatasetGetReq datasetGetReq) {
        Dataset dataset = getOne(Wrappers.<Dataset>lambdaQuery()
                .eq(Dataset::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(Dataset::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(Dataset::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );

        if (null == dataset) {
            throw new BusinessException("数据集不存在");
        }

        DatasetDetailRes datasetDetailRes = BeanCopyUtil.copyFields(dataset, DatasetDetailRes.class);

        List<DatasetJoinRel> datasetJoinRels = datasetJoinRelMapper.selectList(Wrappers.<DatasetJoinRel>lambdaQuery()
                .eq(DatasetJoinRel::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(DatasetJoinRel::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(DatasetJoinRel::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (CollectionUtils.isNotEmpty(datasetJoinRels)) {
            List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = BeanCopyUtil.copyFieldsList(datasetJoinRels, DatasetJoinRelDetailRes.class);
            datasetDetailRes.setDatasetJoinRelDetailRes(datasetJoinRelDetailRes);
        }

        List<DatasetField> datasetFields = datasetFieldMapper.selectList(Wrappers.<DatasetField>lambdaQuery()
                .eq(DatasetField::getEnterpriseNo, datasetGetReq.getEnterpriseNo())
                .eq(DatasetField::getDatasetNo, datasetGetReq.getDatasetNo())
                .eq(DatasetField::getDeleted, DeletedEnum.UN_DELETE.getValue())
        );
        if (CollectionUtils.isNotEmpty(datasetFields)) {
            List<DatasetFieldsDetailRes> datasetFieldsDetailRes = BeanCopyUtil.copyFieldsList(datasetFields, DatasetFieldsDetailRes.class);
            datasetDetailRes.setDatasetFieldsDetailRes(datasetFieldsDetailRes);
        }

        return datasetDetailRes;
    }

    @Override
    public DatasetPreviewRes previewData(DatasetPreviewReq datasetPreviewReq) {
        DatasetGetReq datasetGetReq = new DatasetGetReq();
        datasetGetReq.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetGetReq.setDatasetNo(datasetPreviewReq.getDatasetNo());
        DatasetDetailRes dataset = getDataset(datasetGetReq);

        // 3. 构建预览SQL
        String previewSql = buildPreviewSql(dataset);

        // 4. 执行查询 - 暂时使用简单的JDBC查询
        List<Map<String, Object>> maps = dorisJdbcTemplate.queryForList(previewSql);

        DatasetPreviewRes datasetPreviewRes = new DatasetPreviewRes();
        datasetPreviewRes.setEnterpriseNo(datasetPreviewReq.getEnterpriseNo());
        datasetPreviewRes.setDatasetNo(datasetPreviewReq.getDatasetNo());
        datasetPreviewRes.setData(maps);

        return datasetPreviewRes;
    }

    @Override
    public void validateDataset(DatasetSaveReq datasetSaveReq) {
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq, "数据集配置不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(datasetSaveReq.getDrivingTableName(), "驱动表名称不能为空");
        if (CollectionUtils.isNotEmpty(datasetSaveReq.getDatasetJoinRels())) {
            for (DatasetJoinRelSaveReq datasetJoinRel : datasetSaveReq.getDatasetJoinRels()) {
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetSchemaName(), "关联表schema不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getTargetTableName(), "关联表名称不能为空");
                ValidatorUtil.checkEmptyThrowEx(datasetJoinRel.getJoinCondition(), "关联条件不能为空");
            }
        }

        DatasetDetailRes dataset = BeanCopyUtil.copyFieldsByJson(datasetSaveReq, DatasetDetailRes.class);

        String previewSql = buildPreviewSql(dataset);

        boolean validated = sqlExtractor.validateSQL(previewSql);

        ValidatorUtil.checkTrueThrowEx(validated, "数据集配置校验失败");
    }

    /**
     * 构建预览SQL
     */
    private String buildPreviewSql(DatasetDetailRes dataset) {
        ValidatorUtil.checkEmptyThrowEx(dataset, "数据集不存在");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingSchemaName(), "驱动表schema不能为空");
        ValidatorUtil.checkEmptyThrowEx(dataset.getDrivingTableName(), "驱动表不能为空");


        StringBuilder sql = new StringBuilder();

        List<DatasetFieldsDetailRes> datasetFieldsDetailRes = dataset.getDatasetFieldsDetailRes();
        if (CollectionUtils.isNotEmpty(datasetFieldsDetailRes)) {
            sql.append("SELECT ");
            for (int i = 0; i < datasetFieldsDetailRes.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append(datasetFieldsDetailRes.get(i).getSchemaName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getTableName())
                        .append(".")
                        .append(datasetFieldsDetailRes.get(i).getFieldCode());
            }
        }

        // 基于驱动表构建基础查询
        sql.append(" FROM ")
                .append(dataset.getDrivingSchemaName())
                .append(".")
                .append(dataset.getDrivingTableName());

        List<DatasetJoinRelDetailRes> datasetJoinRelDetailRes = dataset.getDatasetJoinRelDetailRes();
        if (CollectionUtils.isNotEmpty(datasetJoinRelDetailRes)) {
            for (DatasetJoinRelDetailRes joinRel : datasetJoinRelDetailRes) {
                sql.append(" ").append(convertJoinType(joinRel.getJoinType())).append(" ")
                        .append(joinRel.getTargetSchemaName())
                        .append(".")
                        .append(joinRel.getTargetTableName())
                        .append(" ON ")
                        .append(joinRel.getJoinCondition());
            }
        }

        // 添加LIMIT子句
        sql.append(" LIMIT ").append(10);

        return sql.toString();
    }

    /**
     * 转换JOIN类型
     */
    private String convertJoinType(String joinType) {
        if (StringUtils.isEmpty(joinType)) {
            return "INNER JOIN";
        }

        switch (joinType.toLowerCase()) {
            case "innerjoin":
                return "INNER JOIN";
            case "leftjoin":
                return "LEFT JOIN";
            case "rightjoin":
                return "RIGHT JOIN";
            default:
                return "INNER JOIN";
        }
    }
}
