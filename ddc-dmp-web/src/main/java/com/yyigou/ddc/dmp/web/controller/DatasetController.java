package com.yyigou.ddc.dmp.web.controller;

import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.service.sqlextractor.SqlExtractorService;
import com.yyigou.ddc.dmp.web.dto.CommonDTO;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.util.CommonParamsUtil;
import com.yyigou.ddc.dmp.web.vo.dataset.*;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集前端控制器
 */
@RestController
@RequestMapping("/dataset")
public class DatasetController {
    @Resource
    private DatasetService datasetService;

    @Resource
    private SqlExtractorService sqlExtractorService;

    /**
     * 保存数据集
     */
    @PostMapping("/save")
    public DatasetVO save(@RequestBody CommonDTO<DatasetSaveDTO> saveDto) {
        CommonParamsUtil.validateCommonDTO(saveDto);

        DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(saveDto.getParams(), DatasetSaveReq.class);
//        modelSaveReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetSaveReq.setEnterpriseNo("2000002");
        String datasetNo = datasetService.saveDataset(datasetSaveReq);

        DatasetVO datasetVO = new DatasetVO();
        datasetVO.setDatasetNo(datasetNo);
        return datasetVO;
    }

    /**
     * 查看数据集
     */
    @GetMapping("/get")
    public DatasetVO get(@RequestBody CommonDTO<DatasetGetDTO> getDto) {
        CommonParamsUtil.validateCommonDTO(getDto);
        ValidatorUtil.checkEmptyThrowEx(getDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(getDto.getParams(), DatasetGetReq.class);
//        detailGetReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetGetReq.setEnterpriseNo("2000002");
        DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

        if (null == datasetDetailRes) {
            throw new BusinessException("数据集不存在");
        }

        DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
        return datasetVO;
    }

    /**
     * 预览数据集数据
     */
    @PostMapping("/preview")
    public DatasetPreviewVO previewData(@RequestBody CommonDTO<DatasetPreviewDTO> previewDto) {
        CommonParamsUtil.validateCommonDTO(previewDto);
        ValidatorUtil.checkEmptyThrowEx(previewDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(previewDto.getParams(), DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetPreviewReq.setEnterpriseNo("2000002");
        DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

        DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
        return datasetPreviewVO;
    }

    /**
     * 校验数据集配置
     */
    @PostMapping("/validate")
    public DatasetValidateVO validateDataset(@RequestBody CommonDTO<DatasetValidateDTO> validateDto) {
        CommonParamsUtil.validateCommonDTO(validateDto);
        ValidatorUtil.checkEmptyThrowEx(validateDto.getParams().getDatasetNo(), "数据集唯一标识不能为空");

        DatasetValidateVO datasetValidateVO = new DatasetValidateVO();
        datasetValidateVO.setDatasetNo(validateDto.getParams().getDatasetNo());

        DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(validateDto.getParams(), DatasetSaveReq.class);
//        datasetValidateReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        datasetSaveReq.setEnterpriseNo("2000002");
        try {
            datasetService.validateDataset(datasetSaveReq);
            datasetValidateVO.setValid(true);
        } catch (Exception e) {
            datasetValidateVO.setValid(false);
            datasetValidateVO.setMessage(e.getMessage());
        }
        return datasetValidateVO;
    }


    /**
     * 抽取sql字段信息
     */
    @GetMapping("/extract")
    public List<ColumnVO> extract(@RequestBody CommonDTO<SqlExtractorDTO> getDTO) {
        CommonParamsUtil.validateCommonDTO(getDTO);
        String sql = getDTO.getParams().getSql();
        ValidatorUtil.checkEmptyThrowEx(sql, "sql不能为空");

//        SqlExtractorReq getReq = BeanCopyUtil.copyFields(getDTO.getParams(), SqlExtractorReq.class);
//        getReq.setEnterpriseNo(WebSessionUtil.getTenantNo());
        List<ColumnBO> columnBOS = sqlExtractorService.extractTableInfoFromSql(sql);

        List<ColumnVO> columnVOS = columnBOS.stream().map(columnBO -> {
            ColumnVO columnVO = BeanCopyUtil.copyFields(columnBO, ColumnVO.class);
            columnVO.setTableVO(BeanCopyUtil.copyFields(columnBO.getTableBO(), TableVO.class));
            return columnVO;
        }).collect(Collectors.toList());

        return columnVOS;
    }
}