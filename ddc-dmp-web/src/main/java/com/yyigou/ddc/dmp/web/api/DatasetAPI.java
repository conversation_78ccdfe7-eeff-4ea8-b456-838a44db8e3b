package com.yyigou.ddc.dmp.web.api;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.vo.dataset.*;

import java.util.List;

public interface DatasetAPI extends ServiceBase {
    /**
     * 保存数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> save(DatasetSaveDTO params);

    /**
     * 获取数据集
     *
     * @param params
     * @return
     */
    CallResult<DatasetVO> get(DatasetGetDTO params);

    /**
     * 预览数据集数据
     *
     * @param params
     * @return
     */
    CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params);

    /**
     * 校验数据集配置
     *
     * @param params
     * @return
     */
    CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params);

    CallResult<List<ColumnVO>> extract(SqlExtractorDTO params);

}
