package com.yyigou.ddc.dmp.web.provider;

import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.annotation.Interface;
import com.yyigou.ddc.common.service.annotation.Method;
import com.yyigou.ddc.common.service.annotation.MethodType;
import com.yyigou.ddc.dmp.common.exception.BusinessException;
import com.yyigou.ddc.dmp.common.util.BeanCopyUtil;
import com.yyigou.ddc.dmp.common.util.ValidatorUtil;
import com.yyigou.ddc.dmp.manager.exception.CommonExceptionHandler;
import com.yyigou.ddc.dmp.manager.integration.registry.ServiceBaseAbstract;
import com.yyigou.ddc.dmp.model.bo.sqlparser.ColumnBO;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetGetReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetPreviewReq;
import com.yyigou.ddc.dmp.model.req.dataset.DatasetSaveReq;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetDetailRes;
import com.yyigou.ddc.dmp.model.res.dataset.DatasetPreviewRes;
import com.yyigou.ddc.dmp.service.dataset.DatasetService;
import com.yyigou.ddc.dmp.service.sqlextractor.SqlExtractorService;
import com.yyigou.ddc.dmp.web.api.DatasetAPI;
import com.yyigou.ddc.dmp.web.dto.dataset.*;
import com.yyigou.ddc.dmp.web.vo.dataset.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Interface(name = "数据集")
@Component("datasetProvider")
public class DatasetProvider extends ServiceBaseAbstract implements DatasetAPI {
    @Resource
    private DatasetService datasetService;

    @Resource
    private SqlExtractorService sqlExtractorService;

    /**
     * 保存数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.save", name = "保存数据集", processState = 1, requestAuthentication = false, methodType = MethodType.UPDATE)
    public CallResult<DatasetVO> save(DatasetSaveDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");

            DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetSaveReq.class);
//        datasetSaveReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetSaveReq.setEnterpriseNo("2000002");
            String datasetNo = datasetService.saveDataset(datasetSaveReq);

            DatasetVO datasetVO = new DatasetVO();
            datasetVO.setDatasetNo(datasetNo);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 查看数据集
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.get", name = "查看数据集", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetVO> get(DatasetGetDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetGetReq datasetGetReq = BeanCopyUtil.copyFieldsByJson(params, DatasetGetReq.class);
//        datasetGetReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetGetReq.setEnterpriseNo("2000002");
            DatasetDetailRes datasetDetailRes = datasetService.getDataset(datasetGetReq);

            if (null == datasetDetailRes) {
                throw new BusinessException("数据集不存在");
            }

            DatasetVO datasetVO = BeanCopyUtil.copyFields(datasetDetailRes, DatasetVO.class);
            return CallResult.success(datasetVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 预览数据集数据
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.preview", name = "预览数据集数据", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetPreviewVO> previewData(DatasetPreviewDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetPreviewReq datasetPreviewReq = BeanCopyUtil.copyFieldsByJson(params, DatasetPreviewReq.class);
//        datasetPreviewReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetPreviewReq.setEnterpriseNo("2000002");
            DatasetPreviewRes datasetPreviewRes = datasetService.previewData(datasetPreviewReq);

            DatasetPreviewVO datasetPreviewVO = BeanCopyUtil.copyFields(datasetPreviewRes, DatasetPreviewVO.class);
            return CallResult.success(datasetPreviewVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }

    /**
     * 校验数据集配置
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.validate", name = "校验数据集配置", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<DatasetValidateVO> validateDataset(DatasetValidateDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            ValidatorUtil.checkEmptyThrowEx(params.getDatasetNo(), "数据集唯一标识不能为空");

            DatasetSaveReq datasetSaveReq = BeanCopyUtil.copyFieldsByJson(params, DatasetSaveReq.class);
//        datasetValidateReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            datasetSaveReq.setEnterpriseNo("2000002");

            DatasetValidateVO datasetValidateVO = new DatasetValidateVO();
            datasetValidateVO.setDatasetNo(datasetSaveReq.getDatasetNo());

            try {
                datasetService.validateDataset(datasetSaveReq);
                datasetValidateVO.setValid(true);
            } catch (Exception e) {
                datasetValidateVO.setValid(false);
                datasetValidateVO.setMessage(e.getMessage());
            }
            return CallResult.success(datasetValidateVO);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }


    /**
     * 获取单个报表模板信息
     */
    @Override
    @Method(aliasName = "ddc.dmp.dataset.extract", name = "抽取sql元信息", processState = 1, requestAuthentication = false, methodType = MethodType.QUERY)
    public CallResult<List<ColumnVO>> extract(SqlExtractorDTO params) {
        try {
            ValidatorUtil.checkEmptyThrowEx(params, "参数不能为空");
            String sql = params.getSql();
            ValidatorUtil.checkEmptyThrowEx(sql, "sql不能为空");

//        SqlExtractorReq getReq = BeanCopyUtil.copyFields(getDTO.getParams(), SqlExtractorReq.class);
//        getReq.setEnterpriseNo(UserHandleUtils.getOperationModel().getTenantNo());
            List<ColumnBO> columnBOS = sqlExtractorService.extractTableInfoFromSql(sql);

            List<ColumnVO> columnVOS = columnBOS.stream().map(columnBO -> {
                ColumnVO columnVO = BeanCopyUtil.copyFields(columnBO, ColumnVO.class);
                columnVO.setTableVO(BeanCopyUtil.copyFields(columnBO.getTableBO(), TableVO.class));
                return columnVO;
            }).collect(Collectors.toList());

            return CallResult.success(columnVOS);
        } catch (Exception e) {
            return CommonExceptionHandler.handleException(e);
        }
    }
}
